"""
JSON parsing utilities for MindLink Agent Core.
"""

import json
import re
import ast # Though ast is imported, it's not used in the provided snippet. Keep for now.
from typing import Dict, Any, Optional, Tuple, List

try:
    from ..schemas.mindlink import MindLinkRequest, Action
except ImportError:
    # Fallback to absolute imports for standalone execution
    from schemas.mindlink import MindLinkRequest, Action
import logging

logger = logging.getLogger(__name__)

def find_all_balanced_json_blocks(text: str) -> List[str]:
    """
    Find all balanced JSON blocks in the text.
    A balanced block has matching opening and closing braces.
    Improved to handle escaped quotes and nested strings properly.
    """
    stack = []
    blocks = []
    start = None
    in_string = False
    escape = False
    quote_char = ''
    
    for i, ch in enumerate(text):
        if escape:
            escape = False
            continue
            
        if ch == '\\': # Handle escaped characters
            escape = True
            continue
            
        # Handle string literals - only process double quotes for JSON
        if ch == '"':
            if not in_string:
                in_string = True
                quote_char = ch
            elif ch == quote_char: # End of string
                in_string = False
                quote_char = ''
            continue # Don't process braces inside strings
        
        # Only process braces when not inside a string
        if not in_string:
            if ch == '{':
                if not stack: # New top-level block
                    start = i
                stack.append('{')
            elif ch == '}':
                if stack: # Ensure there's a matching open brace
                    stack.pop()
                    if not stack and start is not None: # End of a top-level block
                        candidate_block = text[start:i+1]
                        # Basic validation - should start and end with braces
                        if candidate_block.strip().startswith('{') and candidate_block.strip().endswith('}'):
                            blocks.append(candidate_block)
                        start = None # Reset for next potential block
                # else:
                    # Unmatched closing brace, could log or handle as error if strict
                    # For now, just ignore it to be lenient with surrounding text
    
    # If we have an unclosed block at the end, try to close it
    if stack and start is not None:
        # Add missing closing braces
        missing_braces = len(stack)
        candidate_block = text[start:] + '}' * missing_braces
        if candidate_block.strip().startswith('{'):
            blocks.append(candidate_block)
            logger.debug(f"Auto-closed JSON block with {missing_braces} missing braces")
    
    return blocks


def sanitize_json_string(json_str: str) -> Optional[str]:
    """
    Fix common JSON formatting issues including trailing commas, unterminated strings, and unbalanced quotes.
    Handles multi-line strings properly.
    Returns None if input is empty or None.
    """
    if not json_str:
        logger.debug("sanitize_json_string received empty JSON string")
        return None
    try:
        # Step 1: Remove trailing commas before closing braces and brackets
        result = re.sub(r',(\s*[\]\}])', r'\1', json_str)
        
        # Step 2: Fix unterminated strings by processing the entire content
        # Track quote state across the entire string, not line by line
        in_string = False
        escape_next = False
        quote_positions = []
        
        for i, char in enumerate(result):
            if escape_next:
                escape_next = False
                continue
                
            if char == '\\':
                escape_next = True
                continue
                
            if char == '"':
                quote_positions.append(i)
                in_string = not in_string
        
        # If we have an odd number of quotes, we have an unterminated string
        if len(quote_positions) % 2 == 1:
            # Find the last quote and add a closing quote at the end
            result += '"'
            logger.debug(f"Fixed unterminated string - added closing quote at end")
        
        # Step 3: Fix very long string content that might break JSON parsing
        # Look for content values that are extremely long and might need escaping
        def escape_content_in_quotes(match):
            content = match.group(1)
            # Only escape characters that need escaping in JSON
            # First, protect already properly escaped sequences
            content = content.replace('\\\\', '___DOUBLE_BACKSLASH___')
            content = content.replace('\\"', '___ESCAPED_QUOTE___')
            content = content.replace('\\n', '___ESCAPED_NEWLINE___')
            content = content.replace('\\r', '___ESCAPED_CARRIAGE___')
            content = content.replace('\\t', '___ESCAPED_TAB___')
            content = content.replace('\\/', '___ESCAPED_SLASH___')
            content = content.replace('\\b', '___ESCAPED_BACKSPACE___')
            content = content.replace('\\f', '___ESCAPED_FORMFEED___')

            # Protect any other valid escape sequences (like \u0000)
            content = re.sub(r'\\u[0-9a-fA-F]{4}', lambda m: f'___UNICODE_{m.group(0)[2:]}___', content)

            # Now escape any remaining unescaped backslashes first
            # This is critical - we need to escape single backslashes that aren't part of valid escape sequences
            content = content.replace('\\', '\\\\')

            # Then escape other unescaped characters
            content = content.replace('"', '\\"')  # Escape unescaped quotes
            content = content.replace('\n', '\\n')  # Escape unescaped newlines
            content = content.replace('\r', '\\r')  # Escape unescaped carriage returns
            content = content.replace('\t', '\\t')  # Escape unescaped tabs
            content = content.replace('\b', '\\b')  # Escape unescaped backspace
            content = content.replace('\f', '\\f')  # Escape unescaped form feed

            # Restore the protected sequences
            content = content.replace('___ESCAPED_QUOTE___', '\\"')
            content = content.replace('___ESCAPED_NEWLINE___', '\\n')
            content = content.replace('___ESCAPED_CARRIAGE___', '\\r')
            content = content.replace('___ESCAPED_TAB___', '\\t')
            content = content.replace('___ESCAPED_SLASH___', '\\/')
            content = content.replace('___ESCAPED_BACKSPACE___', '\\b')
            content = content.replace('___ESCAPED_FORMFEED___', '\\f')
            content = content.replace('___DOUBLE_BACKSLASH___', '\\\\')

            # Restore unicode sequences
            content = re.sub(r'___UNICODE_([0-9a-fA-F]{4})___', r'\\u\1', content)

            return f'"{content}"'

        # Apply content escaping to string values
        # This regex finds content within quotes that might need escaping
        # Updated to be more robust with nested quotes and escape sequences
        result = re.sub(r'"((?:[^"\\]|\\.)*)?"', escape_content_in_quotes, result)
        
        # Step 4: Fix common bracket/brace mismatches
        # Count opening and closing braces/brackets outside of strings
        in_string = False
        escape_next = False
        open_braces = 0
        close_braces = 0
        open_brackets = 0
        close_brackets = 0
        
        for char in result:
            if escape_next:
                escape_next = False
                continue
                
            if char == '\\':
                escape_next = True
                continue
                
            if char == '"':
                in_string = not in_string
                continue
                
            if not in_string:
                if char == '{':
                    open_braces += 1
                elif char == '}':
                    close_braces += 1
                elif char == '[':
                    open_brackets += 1
                elif char == ']':
                    close_brackets += 1
        
        # Add missing closing braces/brackets
        if open_braces > close_braces:
            result += '}' * (open_braces - close_braces)
            logger.debug(f"Added {open_braces - close_braces} missing closing braces")
        
        if open_brackets > close_brackets:
            result += ']' * (open_brackets - close_brackets)
            logger.debug(f"Added {open_brackets - close_brackets} missing closing brackets")
        
        return result
        
    except Exception as e:
        logger.error(f"Error sanitizing JSON string: {e}. Input (first 200 chars): {json_str[:200] if json_str else 'None'}", exc_info=True)
        return None # Return None on error to indicate failure


def extract_json_from_text(text: str) -> Optional[str]:
    """
    Extract JSON from text that may contain other content.
    Tries multiple strategies to find valid JSON, prioritizing structured extraction.
    Returns the first successfully parsed JSON string, or None.
    """
    if not text:
        logger.debug("extract_json_from_text: Received empty input text.")
        return None
    
    logger.debug(f"extract_json_from_text: Starting extraction from text (first 500 chars): {text[:500]}")

    # Strategy 1: Try code fences (```json ... ``` or ~~~json ... ~~~)
    # Using named groups for clarity. Allows optional language specifier.
    fence_pattern = r'(?P<fence>```|~~~)(?P<lang>[a-zA-Z0-9]*)\s*\n(?P<content>[\s\S]*?)\s*\n(?P=fence)'
    fence_matches = list(re.finditer(fence_pattern, text))
    logger.debug(f"extract_json_from_text: Found {len(fence_matches)} potential fenced code blocks.")
    
    for match in reversed(fence_matches): # Process last found first
        content_str = match.group('content').strip()
        logger.debug(f"extract_json_from_text: Evaluating fenced content (first 200 chars): {content_str[:200]}")
        if content_str.startswith('{') and content_str.endswith('}'):
            try:
                json.loads(content_str)
                logger.info("extract_json_from_text: Successfully parsed JSON directly from fenced content.")
                return content_str
            except json.JSONDecodeError as e:
                logger.warning(f"extract_json_from_text: Direct JSON parsing of fenced content failed: {e}. Content preview: {content_str[:100]}... Attempting sanitization.")
                sanitized_content = sanitize_json_string(content_str)
                if sanitized_content:
                    try:
                        json.loads(sanitized_content)
                        logger.info("extract_json_from_text: Successfully parsed sanitized JSON from fenced content.")
                        return sanitized_content
                    except json.JSONDecodeError as e_sanitized:
                        logger.warning(f"extract_json_from_text: Sanitized JSON from fenced content also failed to parse: {e_sanitized}. Sanitized preview: {sanitized_content[:100]}...")
                        # Try one more aggressive sanitization for escape sequences
                        try:
                            # Last resort: try to fix common escape sequence issues
                            ultra_sanitized = content_str.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')
                            if ultra_sanitized.startswith('{') and ultra_sanitized.endswith('}'):
                                json.loads(ultra_sanitized)
                                logger.info("extract_json_from_text: Successfully parsed ultra-sanitized JSON from fenced content.")
                                return ultra_sanitized
                        except:
                            pass
                else:
                    logger.warning("extract_json_from_text: Sanitization of fenced content resulted in None or empty string.")
        else:
            logger.debug("extract_json_from_text: Fenced content does not start/end with {} braces.")
        # If this fenced block is not valid JSON, continue to the next or other strategies.

    # Strategy 1b: Try HTML <pre><code> blocks
    html_pattern = r'<pre><code>([\s\S]*?)</code></pre>'
    html_content_matches = re.findall(html_pattern, text)
    logger.debug(f"extract_json_from_text: Found {len(html_content_matches)} potential HTML <pre><code> blocks.")
    for content_str in reversed(html_content_matches):
        content_str = content_str.strip()
        logger.debug(f"extract_json_from_text: Evaluating HTML <pre><code> content (first 200 chars): {content_str[:200]}")
        if content_str.startswith('{') and content_str.endswith('}'):
            try:
                json.loads(content_str)
                logger.info("extract_json_from_text: Successfully parsed JSON directly from HTML <pre><code> content.")
                return content_str
            except json.JSONDecodeError as e:
                logger.warning(f"extract_json_from_text: Direct JSON parsing of HTML <pre><code> content failed: {e}. Attempting sanitization.")
                sanitized_content = sanitize_json_string(content_str)
                if sanitized_content:
                    try:
                        json.loads(sanitized_content)
                        logger.info("extract_json_from_text: Successfully parsed sanitized JSON from HTML <pre><code> content.")
                        return sanitized_content
                    except json.JSONDecodeError as e_sanitized:
                        logger.warning(f"extract_json_from_text: Sanitized JSON from HTML <pre><code> content also failed to parse: {e_sanitized}")
                else:
                    logger.warning("extract_json_from_text: Sanitization of HTML <pre><code> content resulted in None or empty string.")
        else:
            logger.debug("extract_json_from_text: HTML <pre><code> content does not start/end with {} braces.")

    # Strategy 2: Extract balanced JSON blocks from the whole text
    candidate_blocks = find_all_balanced_json_blocks(text)
    logger.debug(f"extract_json_from_text: Found {len(candidate_blocks)} potential balanced JSON blocks.")
    if candidate_blocks:
        for block_candidate in reversed(candidate_blocks): # Process last found first (often largest/most complete)
            logger.debug(f"extract_json_from_text: Evaluating balanced block candidate (first 200 chars): {block_candidate[:200]}")
            try:
                json.loads(block_candidate)
                logger.info("extract_json_from_text: Successfully parsed JSON directly from a balanced block.")
                return block_candidate
            except json.JSONDecodeError as e:
                logger.warning(f"extract_json_from_text: Direct JSON parsing of balanced block failed: {e}. Attempting sanitization.")
                # Ensure this block is correctly indented under the except clause
                sanitized_block = sanitize_json_string(block_candidate)
                if sanitized_block:
                    try:
                        json.loads(sanitized_block)
                        logger.info("extract_json_from_text: Successfully parsed sanitized JSON from a balanced block.")
                        return sanitized_block
                    except json.JSONDecodeError as e_sanitized:
                        logger.warning(f"extract_json_from_text: Sanitized JSON from balanced block also failed to parse: {e_sanitized}")
                else: # This else corresponds to 'if sanitized_block:'
                    logger.warning("extract_json_from_text: Sanitization of balanced block resulted in None or empty string.")
        # This log should be outside the loop, after all candidates are processed, if this strategy fails.
        # However, the original structure had it inside the 'if candidate_blocks:'
        # For now, keeping it aligned with the loop's completion if no return happened.
        logger.debug("extract_json_from_text: All balanced block candidates failed direct and sanitized parsing within this strategy.")
            
    # Strategy 3: Look for anything that resembles JSON with a general regex (least reliable)
    json_like_pattern = r"(\{[\s\S]*?\})" 
    json_like_matches = re.findall(json_like_pattern, text)
    logger.debug(f"extract_json_from_text: Found {len(json_like_matches)} JSON-like regex matches.")
    if json_like_matches:
        for match_str in reversed(json_like_matches): # Process last found first
            match_str = match_str.strip()
            if len(match_str) > 10 and match_str.startswith('{') and match_str.endswith('}'): 
                logger.debug(f"extract_json_from_text: Evaluating JSON-like regex match (first 200 chars): {match_str[:200]}")
                try:
                    json.loads(match_str)
                    logger.info("extract_json_from_text: Successfully parsed JSON directly from regex match.")
                    return match_str 
                except json.JSONDecodeError as e:
                    logger.warning(f"extract_json_from_text: Direct JSON parsing of regex match failed: {e}. Attempting sanitization.")
                    sanitized_match = sanitize_json_string(match_str)
                    if sanitized_match:
                        try:
                            json.loads(sanitized_match)
                            logger.info("extract_json_from_text: Successfully parsed sanitized JSON from regex match.")
                            return sanitized_match
                        except json.JSONDecodeError as e_sanitized:
                            logger.warning(f"extract_json_from_text: Sanitized JSON from regex match also failed to parse: {e_sanitized}")
                    else:
                        logger.warning("extract_json_from_text: Sanitization of regex match resulted in None or empty string.")
        logger.debug("extract_json_from_text: All JSON-like regex matches failed direct and sanitized parsing.")

    logger.warning(f"extract_json_from_text: Failed to extract valid JSON using all strategies. Input (first 500 chars): {text[:500]}")
    return None


def create_fallback_request(error_message: str) -> MindLinkRequest:
    truncation_limit = 500
    truncated_message = (error_message[:truncation_limit] + '...') if len(error_message) > truncation_limit else error_message
    fallback_action = Action(
        tool_name="echo",
        parameters={"message": f"Error parsing response: {truncated_message}. Please try again with proper JSON format."}
    )
    fallback_request = MindLinkRequest(
        action=fallback_action,
        reasoning=f"Fallback action due to parsing error: {truncated_message}",
        thought="The previous response had JSON formatting issues"
    )
    return fallback_request

def extract_reasoning_from_text(text: str) -> Optional[str]:
    # Placeholder for brevity, assume original implementation
    reasoning_patterns = [
        r'reasoning["\s:]+([^"}{,]+)',r'reason["\s:]+([^"}{,]+)',
        r'because ([^.]+)',r'I need to ([^.]+)'
    ]
    for pattern in reasoning_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match: return match.group(1).strip()
    return None

def extract_tool_info(text: str) -> Tuple[Optional[str], Optional[Dict]]:
    # Placeholder for brevity, assume original implementation
    tool_match = re.search(r'tool_name["\s:]+([^",\s]+)', text)
    tool_name = tool_match.group(1).strip() if tool_match else None
    params = {}
    if tool_name == "create_file": 
        return extract_create_file_params(text, tool_name) # Assume this exists
    param_block_match = re.search(r'parameters["\s:]+({[^{}]*})', text)
    if param_block_match:
        param_block = param_block_match.group(1)
        try: params = json.loads(param_block)
        except: pass # Simplified
    return tool_name, params

def extract_create_file_params(text: str, tool_name: str) -> Tuple[Optional[str], Optional[Dict]]:
    # Placeholder for brevity, assume original implementation
    params = {}
    path_match = re.search(r'path["\s:]+["\']([^"\']+)["\']', text)
    if path_match: params['path'] = path_match.group(1).strip()
    else: params['path'] = "output.txt"
    # Simplified content extraction
    content_match = re.search(r'content["\s:]+["\'](.*?)["\']', text, re.DOTALL)
    if content_match: params['content'] = content_match.group(1) 
    else: params['content'] = ""
    return tool_name, params


def parse_llm_response(response: str) -> Tuple[Optional[MindLinkRequest], Optional[str]]:
    if not response or len(response.strip()) < 5:
        logger.warning("parse_llm_response: Received empty or too short response.")
        return create_fallback_request("Empty or too short response"), None
    
    json_candidate = extract_json_from_text(response)
    
    if not json_candidate:
        logger.warning(f"parse_llm_response: extract_json_from_text returned None. Attempting tool info extraction from raw response (first 500 chars): {response[:500]}")
        tool_name, params = extract_tool_info(response) # Try to get anything from the raw text
        if tool_name:
            logger.info(f"parse_llm_response: Salvaged tool_name '{tool_name}' and params using extract_tool_info.")
            try:
                constructed_json_data = {
                    "action": {"tool_name": tool_name, "parameters": params or {}},
                    "reasoning": extract_reasoning_from_text(response) or "Salvaged from raw text after JSON extraction failure."
                }
                # This reconstructed JSON is assumed to be valid by construction
                json_candidate = json.dumps(constructed_json_data, ensure_ascii=False)
                logger.info(f"parse_llm_response: Reconstructed JSON from salvaged parts: {json_candidate[:200]}...")
            except Exception as e:
                logger.error(f"parse_llm_response: Error constructing JSON from salvaged parts: {e}", exc_info=True)
                return create_fallback_request(f"Error constructing JSON from salvaged parts: {e}"), None
        else:
            logger.warning("parse_llm_response: extract_tool_info also failed to find a tool_name.")
            return create_fallback_request("No valid JSON found and no tool information could be salvaged."), None

    # If json_candidate is from extract_json_from_text, it's already been validated by json.loads.
    # If json_candidate was successfully returned by extract_json_from_text,
    # it means it's already confirmed to be valid JSON (parsed directly or after sanitization).
    # If json_candidate was reconstructed via extract_tool_info, it was made via json.dumps(), so it's also valid.
    # Therefore, an additional sanitize_json_string(json_candidate) here should be redundant.
    
    json_to_parse = json_candidate # Use the candidate directly
    logger.info(f"parse_llm_response: Final JSON candidate selected for parsing (first 200 chars): {json_to_parse[:200]}")
    
    try:
        data = json.loads(json_to_parse)
    except json.JSONDecodeError as e:
        # This indicates an issue if extract_json_from_text or the reconstruction path didn't yield perfectly valid JSON.
        logger.error(f"parse_llm_response: CRITICAL - Failed to parse JSON candidate that was believed to be valid: '{json_to_parse[:200]}...'. Error: {e}", exc_info=True)
        return create_fallback_request(f"Internal Error: Invalid JSON candidate provided for final parsing: {e}"), None
    
    # Schema validation and MindLinkRequest creation
    logger.debug(f"parse_llm_response: Successfully loaded JSON into dict. Data: {str(data)[:300]}...")
    action_data = data.get('action')
    if not isinstance(action_data, dict) or 'tool_name' not in action_data:
        logger.warning(f"parse_llm_response: Parsed JSON data lacks a valid 'action' with 'tool_name'. Data: {data}")
        return create_fallback_request("Parsed JSON lacks a valid 'action' with 'tool_name'."), None

    # Ensure parameters exists
    action_data.setdefault('parameters', {})
    
    # Fill in optional fields if missing, for Pydantic validation
    data.setdefault('reasoning', '')
    data.setdefault('thought', '')
    
    try:
        request = MindLinkRequest.model_validate(data)
        logger.info(f"parse_llm_response: Successfully parsed MindLinkRequest: tool_name='{request.action.tool_name}'")
        return request, None
    except Exception as e: # Pydantic validation error
        logger.error(f"parse_llm_response: MindLinkRequest schema validation error: {e}. Data: {data}", exc_info=True)
        # Attempt simplified construction if validation fails, as a last resort for known tool_name
        tool_name_salvage = action_data.get('tool_name')
        if tool_name_salvage:
            try:
                logger.warning(f"parse_llm_response: Attempting simplified MindLinkRequest construction for tool '{tool_name_salvage}'.")
                simplified_action = Action(tool_name=tool_name_salvage, parameters=action_data.get('parameters', {}))
                request = MindLinkRequest(action=simplified_action, 
                                          reasoning=data.get('reasoning', 'Schema validation fallback'), 
                                          thought=data.get('thought', ''))
                return request, None # Return with a note that it's a simplified/fallback construction
            except Exception as final_e:
                logger.error(f"parse_llm_response: Simplified MindLinkRequest construction also failed: {final_e}", exc_info=True)
                return create_fallback_request(f"Schema validation error: {e}, and fallback construction failed: {final_e}"), None
        return create_fallback_request(f"Schema validation error and no tool_name for salvage: {e}"), None

def safe_parse_llm_response(text: str) -> Tuple[Optional[MindLinkRequest], Optional[str]]:
    logger.debug(f"safe_parse_llm_response: Raw text (first 500 chars): {text[:500] if text else 'None'}")
    if not text:
        error_msg = "Received empty text in safe_parse_llm_response"
        logger.warning(error_msg)
        return create_fallback_request(error_msg), None
    try:
        return parse_llm_response(text)
    except Exception as e:
        error_msg = f"Unexpected parsing error in safe_parse_llm_response: {e}"
        logger.error(error_msg, exc_info=True)
        return create_fallback_request(error_msg), None
