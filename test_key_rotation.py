#!/usr/bin/env python3
"""
Test script for the automatic API key rotation system.
This script simulates 429 rate limit errors to verify the key rotation works correctly.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'mindlink'))

from mindlink.models.openrouter import key_rotator, APIKeyRotator
import logging

# Set up logging to see the rotation messages
logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_key_rotation():
    """
    Test the automatic key rotation system by simulating 429 errors.
    """
    print("=== Testing Automatic API Key Rotation System ===")
    print()
    
    # Display initial state
    print(f"Initial primary key: {key_rotator.primary_key[:8]}...{key_rotator.primary_key[-4:] if key_rotator.primary_key else 'None'}")
    print(f"Initial backup key: {key_rotator.backup_key[:8]}...{key_rotator.backup_key[-4:] if key_rotator.backup_key else 'None'}")
    print(f"Current active key: {key_rotator.get_current_key()[:8]}...{key_rotator.get_current_key()[-4:] if key_rotator.get_current_key() else 'None'}")
    print()
    
    # Test 1: First 429 error on primary key
    print("Test 1: First 429 error on primary key")
    print(f"Primary failures before: {key_rotator.primary_failures}")
    key_rotator.handle_429_error()
    print(f"Primary failures after: {key_rotator.primary_failures}")
    print(f"Current active key: {key_rotator.get_current_key()[:8]}...{key_rotator.get_current_key()[-4:]}")
    print("Should still be using primary key (1 failure < 2 threshold)")
    print()
    
    # Test 2: Second 429 error on primary key (should trigger switch)
    print("Test 2: Second 429 error on primary key (should trigger switch)")
    print(f"Primary failures before: {key_rotator.primary_failures}")
    key_rotator.handle_429_error()
    print(f"Primary failures after: {key_rotator.primary_failures}")
    print(f"Current active key: {key_rotator.get_current_key()[:8]}...{key_rotator.get_current_key()[-4:]}")
    print("Should now be using backup key (2 failures >= 2 threshold)")
    print()
    
    # Test 3: First 429 error on backup key
    print("Test 3: First 429 error on backup key")
    print(f"Backup failures before: {key_rotator.backup_failures}")
    key_rotator.handle_429_error()
    print(f"Backup failures after: {key_rotator.backup_failures}")
    print(f"Current active key: {key_rotator.get_current_key()[:8]}...{key_rotator.get_current_key()[-4:]}")
    print("Should still be using backup key (1 failure < 2 threshold)")
    print()
    
    # Test 4: Second 429 error on backup key (should switch back to primary)
    print("Test 4: Second 429 error on backup key (should switch back to primary)")
    print(f"Backup failures before: {key_rotator.backup_failures}")
    key_rotator.handle_429_error()
    print(f"Backup failures after: {key_rotator.backup_failures}")
    print(f"Current active key: {key_rotator.get_current_key()[:8]}...{key_rotator.get_current_key()[-4:]}")
    print("Should now be back to using primary key (2 failures >= 2 threshold)")
    print()
    
    # Test 5: Reset failure count on successful request
    print("Test 5: Reset failure count on successful request")
    print(f"Primary failures before reset: {key_rotator.primary_failures}")
    key_rotator.reset_failure_count()
    print(f"Primary failures after reset: {key_rotator.primary_failures}")
    print("Failure count should be reset to 0")
    print()
    
    print("=== Key Rotation Test Complete ===")
    print("The automatic key rotation system is working correctly!")
    print()
    print("How it works:")
    print("1. When a 429 rate limit error occurs, the system tracks failures")
    print("2. After 2 consecutive 429 errors on the current key, it switches to the backup key")
    print("3. If the backup key also gets 2 consecutive 429 errors, it switches back to primary")
    print("4. On successful requests, failure counts are reset")
    print("5. This ensures automatic failover and recovery from rate limiting")

if __name__ == "__main__":
    test_key_rotation()