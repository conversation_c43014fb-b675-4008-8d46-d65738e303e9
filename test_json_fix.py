#!/usr/bin/env python3
"""Test script to verify JSON parsing fix"""

import sys
import os
sys.path.append('.')

from mindlink.utils.json_parser import extract_json_from_text, sanitize_json_string
import json

def test_json_parsing():
    """Test the JSON parsing with problematic escape sequences"""
    
    # Test case 1: Simple case with backslashes
    test1 = '''```json
{
  "action": {
    "tool_name": "create_file",
    "parameters": {
      "path": "D:/3/test.py",
      "content": "import os\\nprint(\\"hello\\")"
    }
  }
}
```'''
    
    print("Test 1: Simple escape sequences")
    result1 = extract_json_from_text(test1)
    if result1:
        try:
            parsed1 = json.loads(result1)
            print("✅ SUCCESS: Test 1 passed")
            print(f"   Tool: {parsed1['action']['tool_name']}")
        except Exception as e:
            print(f"❌ FAILED: Test 1 parse error: {e}")
    else:
        print("❌ FAILED: Test 1 extraction failed")
    
    # Test case 2: Complex case with Windows paths and code
    test2 = '''```json
{
  "action": {
    "tool_name": "create_file",
    "parameters": {
      "path": "D:/3/complex_file.py",
      "content": "import logging\\nimport time\\nfrom fastapi import FastAPI\\n\\napp = FastAPI()\\n\\<EMAIL>(\\"/\\\")\\ndef read_root():\\n    return {\\"message\\": \\"Hello World\\"}"
    }
  }
}
```'''
    
    print("\nTest 2: Complex escape sequences")
    result2 = extract_json_from_text(test2)
    if result2:
        try:
            parsed2 = json.loads(result2)
            print("✅ SUCCESS: Test 2 passed")
            print(f"   Tool: {parsed2['action']['tool_name']}")
            print(f"   Content length: {len(parsed2['action']['parameters']['content'])}")
        except Exception as e:
            print(f"❌ FAILED: Test 2 parse error: {e}")
    else:
        print("❌ FAILED: Test 2 extraction failed")
    
    # Test case 3: Direct sanitization test
    print("\nTest 3: Direct sanitization")
    problematic_json = '{"content": "import os\\nprint(\\"test\\")"}'
    sanitized = sanitize_json_string(problematic_json)
    if sanitized:
        try:
            json.loads(sanitized)
            print("✅ SUCCESS: Test 3 passed")
        except Exception as e:
            print(f"❌ FAILED: Test 3 sanitization error: {e}")
    else:
        print("❌ FAILED: Test 3 sanitization returned None")

if __name__ == "__main__":
    test_json_parsing()
