# MindLink Command Comprehension System

## Overview

The MindLink Command Comprehension System is an intelligent preprocessing layer that analyzes user commands before they reach the main LLM. This system uses Mistral Small 3.1 model via OpenRouter API to extract intent and entities from user commands, providing better understanding and more accurate responses.

## Features

### 🧠 Intelligent Command Analysis
- **Intent Detection**: Automatically identifies the primary purpose of user commands
- **Entity Extraction**: Extracts specific details like files, numbers, names, locations, etc.
- **Confidence Assessment**: Evaluates how confident the analysis is
- **Ambiguity Detection**: Identifies unclear parts that need clarification

### 🔄 Dual Analysis Approach
1. **Quick Pattern Matching**: Fast analysis using predefined patterns
2. **AI-Powered Analysis**: Advanced analysis using Mistral Small 3.1 model

### 📝 Command Enhancement
- Enhances commands with extracted intent and entity information
- Provides structured context to the main LLM
- Improves planning and execution accuracy

## Architecture

```
User Command → Command Comprehension Engine → Enhanced Command → Main LLM → Response
                     ↓
              [Intent + Entities + Confidence]
```

## Configuration

### API Key Setup

The system requires an OpenRouter API key to access the Mistral Small 3.1 model:

```bash
# Set in environment variable
export OPENROUTER_API_KEY="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"

# Or add to .env file
OPENROUTER_API_KEY=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
```

### Agent Initialization

```python
from mindlink.agent import AgentOS
from mindlink.models.openrouter import OpenRouterModel

# Initialize with command comprehension enabled
llm = OpenRouterModel(api_key=api_key, model_name="deepseek-r1")
agent = AgentOS(
    llm=llm,
    system_prompt_template=DEFAULT_SYSTEM_PROMPT,
    enable_command_comprehension=True  # Enable intelligent preprocessing
)
```

## Intent Categories

The system recognizes the following intent categories:

- **file_operation**: Create, read, write, delete, modify files
- **code_generation**: Generate, implement, build code
- **data_analysis**: Analyze, process, calculate data
- **web_search**: Search for information online
- **system_command**: Run terminal/shell commands
- **question_answer**: Answer questions, explain concepts
- **general_task**: Other general tasks

## Entity Types

The system extracts various entity types:

- **files**: File paths and names
- **numbers**: Numeric values
- **quoted_text**: Text in quotes
- **names**: Person, place, or object names
- **times**: Time-related information
- **locations**: Geographic locations

## Usage Examples

### Basic Command Analysis

```python
from mindlink.command_comprehension import CommandComprehensionEngine

engine = CommandComprehensionEngine(api_key)
analysis = engine.analyze_command("Create a Python file called hello.py")

print(f"Intent: {analysis.intent}")  # file_operation
print(f"Confidence: {analysis.confidence.value}")  # high
print(f"Entities: {analysis.entities}")  # {'files': ['hello.py']}
```

### Handling Ambiguous Commands

```python
analysis = engine.analyze_command("Fix the bug")

if analysis.clarification_needed:
    question = engine.generate_clarification_question(analysis)
    print(question)  # "I need clarification about your request..."
```

### Integration with Agent

```python
# The agent automatically preprocesses commands
result = agent.run("Create a web scraper for product prices")
# Command is analyzed and enhanced before planning
```

## Command Flow

1. **User Input**: User provides a command
2. **Quick Analysis**: Pattern-based analysis for common intents
3. **AI Analysis**: If needed, use Mistral Small 3.1 for complex analysis
4. **Confidence Check**: Evaluate analysis confidence
5. **Clarification**: If low confidence, generate clarification questions
6. **Enhancement**: Add intent and entity information to command
7. **Main Processing**: Enhanced command goes to main LLM

## Confidence Levels

- **HIGH**: Clear intent and entities detected
- **MEDIUM**: Intent detected but some ambiguity
- **LOW**: Unclear intent, clarification needed

## Error Handling

- Graceful fallback to original command if analysis fails
- Comprehensive logging for debugging
- Timeout protection for API calls
- Retry mechanisms for transient failures

## Testing

Run the test script to verify functionality:

```bash
python test_command_comprehension.py
```

This will test:
- Command analysis with various input types
- Integration with the MindLink Agent
- Error handling scenarios

## Performance Considerations

- Quick pattern matching for common commands
- AI analysis only when needed
- Caching of analysis results
- Configurable timeout settings

## Security

- API keys are handled securely
- No sensitive data logged
- Input sanitization for API calls
- Rate limiting compliance

## Troubleshooting

### Common Issues

1. **API Key Not Found**
   - Ensure OPENROUTER_API_KEY is set
   - Check .env file configuration

2. **Analysis Fails**
   - Check internet connectivity
   - Verify API key validity
   - Review logs for error details

3. **Low Confidence Results**
   - Commands may be too ambiguous
   - Try more specific language
   - Check for typos or unclear terms

### Logging

Enable debug logging to see detailed analysis:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Future Enhancements

- Support for additional AI models
- Custom intent categories
- Learning from user feedback
- Multi-language support
- Context-aware analysis

## API Reference

### CommandComprehensionEngine

```python
class CommandComprehensionEngine:
    def __init__(self, api_key: str)
    def analyze_command(self, command: str) -> CommandAnalysis
    def generate_clarification_question(self, analysis: CommandAnalysis) -> str
```

### CommandAnalysis

```python
@dataclass
class CommandAnalysis:
    intent: str
    entities: Dict[str, Any]
    confidence: ConfidenceLevel
    ambiguous_parts: Optional[List[str]]
    clarification_needed: bool
    raw_command: str
```

## Contributing

To contribute to the command comprehension system:

1. Add new intent patterns in `intent_patterns`
2. Enhance entity extraction methods
3. Improve confidence assessment algorithms
4. Add support for new AI models
5. Extend test coverage

---

*This system represents a significant advancement in AI agent command understanding, providing more accurate and context-aware responses to user requests.*