#!/usr/bin/env python3
"""
Test script to verify the multi-file creation bug fix.
This script tests whether all 40 files are created properly after the planning logic fixes.
"""

import os
import sys
import shutil
import tempfile
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import with proper module path handling
try:
    from agent import MindLinkAgent
    from tools import get_all_tools
except ImportError:
    # Alternative import method
    import importlib.util
    import sys
    
    # Load agent module
    agent_spec = importlib.util.spec_from_file_location("agent", "agent.py")
    agent_module = importlib.util.module_from_spec(agent_spec)
    agent_spec.loader.exec_module(agent_module)
    MindLinkAgent = agent_module.MindLinkAgent
    
    # Load tools module
    tools_spec = importlib.util.spec_from_file_location("tools", "tools.py")
    tools_module = importlib.util.module_from_spec(tools_spec)
    tools_spec.loader.exec_module(tools_module)
    get_all_tools = tools_module.get_all_tools

def test_40_file_creation():
    """
    Test creating exactly 40 files to verify the bug fix.
    """
    print("=== Testing 40 File Creation Bug Fix ===")
    
    # Create a temporary directory for testing
    test_dir = tempfile.mkdtemp(prefix="test_40_files_")
    print(f"Test directory: {test_dir}")
    
    try:
        # Initialize agent
        tools = get_all_tools()
        agent = MindLinkAgent(tools=tools, session_id="test_40_files")
        
        # Test goal: create exactly 40 files
        goal = "Create exactly 40 Python files for a comprehensive web application project. Each file should be around 50 lines and contain meaningful code for different modules like authentication, database, API endpoints, frontend components, utilities, tests, etc. Make this a realistic project structure."
        
        print(f"\nGoal: {goal}")
        print("\nStarting file creation...")
        
        # Run the agent
        success, message, response = agent.run(goal)
        
        print(f"\nAgent execution completed:")
        print(f"Success: {success}")
        print(f"Message: {message[:200]}..." if len(message) > 200 else f"Message: {message}")
        
        # Count created files
        created_files = []
        for root, dirs, files in os.walk(test_dir):
            for file in files:
                if file.endswith('.py'):
                    created_files.append(os.path.join(root, file))
        
        # Also check the default creation directory (D:/3/)
        default_dir = "D:/3"
        if os.path.exists(default_dir):
            for root, dirs, files in os.walk(default_dir):
                for file in files:
                    if file.endswith('.py') and 'test_40_files' in root:
                        created_files.append(os.path.join(root, file))
        
        print(f"\n=== RESULTS ===")
        print(f"Files created: {len(created_files)}")
        print(f"Target: 40 files")
        print(f"Success rate: {len(created_files)/40*100:.1f}%")
        
        if len(created_files) >= 40:
            print("✅ SUCCESS: All 40 files created!")
            print("🎉 Multi-file creation bug has been FIXED!")
        elif len(created_files) >= 35:
            print("⚠️  PARTIAL SUCCESS: Most files created (acceptable variance)")
        else:
            print("❌ FAILURE: Not enough files created")
            print("🐛 Multi-file creation bug still exists")
        
        # Show some created files
        print(f"\nSample created files:")
        for i, file_path in enumerate(created_files[:10]):
            print(f"  {i+1}. {os.path.basename(file_path)}")
        
        if len(created_files) > 10:
            print(f"  ... and {len(created_files) - 10} more files")
        
        # Check multi-file context state
        if hasattr(agent, '_multi_file_request_context') and agent._multi_file_request_context:
            ctx = agent._multi_file_request_context
            print(f"\n=== CONTEXT STATE ===")
            print(f"Files created count: {ctx.get('files_created_count', 'N/A')}")
            print(f"Files remaining: {ctx.get('num_files_remaining', 'N/A')}")
            print(f"Original total: {ctx.get('original_total_files', 'N/A')}")
        else:
            print(f"\n=== CONTEXT STATE ===")
            print("Multi-file context: Cleared (expected after completion)")
        
        return len(created_files) >= 35  # Allow some variance
        
    except Exception as e:
        print(f"❌ ERROR during test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up test directory
        try:
            shutil.rmtree(test_dir)
            print(f"\nCleaned up test directory: {test_dir}")
        except Exception as e:
            print(f"Warning: Could not clean up test directory: {e}")

def test_smaller_batch():
    """
    Test creating a smaller batch (10 files) to ensure basic functionality.
    """
    print("\n=== Testing 10 File Creation (Control Test) ===")
    
    try:
        # Initialize agent
        tools = get_all_tools()
        agent = MindLinkAgent(tools=tools, session_id="test_10_files")
        
        # Test goal: create 10 files
        goal = "Create 10 Python files for a simple calculator project. Each file should be around 30 lines with different mathematical operations."
        
        print(f"Goal: {goal}")
        
        # Run the agent
        success, message, response = agent.run(goal)
        
        print(f"Control test completed: {success}")
        return success
        
    except Exception as e:
        print(f"❌ ERROR in control test: {e}")
        return False

if __name__ == "__main__":
    print("Multi-File Creation Bug Fix Test")
    print("=" * 50)
    
    # Run control test first
    control_success = test_smaller_batch()
    
    # Run main test
    main_success = test_40_file_creation()
    
    print("\n" + "=" * 50)
    print("FINAL RESULTS:")
    print(f"Control test (10 files): {'✅ PASS' if control_success else '❌ FAIL'}")
    print(f"Main test (40 files): {'✅ PASS' if main_success else '❌ FAIL'}")
    
    if main_success:
        print("\n🎉 CONGRATULATIONS! The multi-file creation bug has been FIXED!")
        print("The agent can now successfully create all requested files.")
    else:
        print("\n🐛 The multi-file creation bug still needs more work.")
        print("Check the planning logic and context management.")