"""
Entry point for MindLink Agent Core.
"""

import argparse
import os
import sys
from typing import Dict, Any, Optional
import json
import asyncio

# Add current directory to Python path for local imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Optional dotenv import
try:
    from dotenv import load_dotenv
    has_dotenv = True
except ImportError:
    has_dotenv = False

# Import from local modules
try:
    from .config import load_config, OPENROUTER_CONFIG
    from .models.openrouter import OpenRouterModel
    from .executor import ToolExecutor
    from .schemas.mindlink import MindLinkRequest, MindLinkResponse
    from .models.llm import LLMInterface
except ImportError:
    # Fallback to absolute imports for standalone execution
    from config import load_config, OPENROUTER_CONFIG
    from models.openrouter import OpenRouterModel
    from executor import ToolExecutor
    from schemas.mindlink import MindLinkRequest, MindLinkResponse
    from models.llm import LLMInterface

from fastapi import FastAP<PERSON>
from uvicorn import run as uvicorn_run

app = FastAPI()
executor = ToolExecutor()

@app.post("/invoke_tool")
async def invoke_tool(request: MindLinkRequest) -> MindLinkResponse:
    return executor.execute(request)

def create_llm(config: Dict[str, Any]):
    """
    Create an LLM instance based on configuration, merging defaults with overrides.
    """
    provider = config.get("llm", {}).get("provider", "").lower()

    # Select base config for LLM provider
    if provider == "openai":
        raise ValueError("OpenAI provider is no longer supported. Please use OpenRouter with the free Mistral model.")
    elif provider == "openrouter":
        base_conf = OPENROUTER_CONFIG.copy()
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}. Supported providers: openrouter")

    # Merge default settings with user overrides
    user_conf = config.get("llm", {})
    llm_conf = {**base_conf, **user_conf}

    return OpenRouterModel(
        model_name=llm_conf.get("model_name"),
        api_key=llm_conf.get("api_key"),
        temperature=llm_conf.get("temperature"),
        max_tokens=llm_conf.get("max_tokens"),
        top_p=llm_conf.get("top_p")
    )


def main():
    """
    Main entry point for the MindLink Agent Core.
    """
    # Load environment variables from .env file if dotenv is available
    if has_dotenv:
        load_dotenv()

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="MindLink Agent Core")
    parser.add_argument("goal", nargs="?", help="The goal for the agent to accomplish")
    parser.add_argument("--config", help="Path to configuration file")
    parser.add_argument("--interactive", "-i", action="store_true", help="Run in interactive mode")
    parser.add_argument("--serve", "-s", action="store_true", help="Start the API server")
    args = parser.parse_args()

    # Load configuration
    config = load_config(args.config)

    # Create LLM
    try:
        llm = create_llm(config)
    except ValueError as e:
        print(f"Error: {e}")
        sys.exit(1)

    # Create Agent OS
    from agent import AgentOS
    agent = AgentOS(
        llm=llm,
        system_prompt_template=config["agent"]["system_prompt_template"],
        max_steps=config["agent"]["max_steps"]
    )

    if args.interactive:
        # Interactive mode
        print("MindLink Agent Core - Interactive Mode")
        print("Enter your goal or 'exit' to quit")

        while True:
            goal = input("\nGoal: ")

            if goal.lower() in ["exit", "quit", "q"]:
                break

            if not goal:
                continue

            print("\nRunning agent...")
            success, result, _ = agent.run(goal)

            print("\n" + "="*50)
            print("Result:", "Success" if success else "Incomplete")
            print(result)
            print("="*50)

    elif args.goal:
        # Single goal mode
        print(f"Goal: {args.goal}")
        success, result, _ = agent.run(args.goal)

        print("\n" + "="*50)
        print("Result:", "Success" if success else "Incomplete")
        print(result)
        print("="*50)

    elif args.serve:
        # Debug log: print server start info
        print(f"[DEBUG] Starting FastAPI server on {config['server']['host']}:{config['server']['port']}")
        uvicorn_run(
            app,
            host=config["server"]["host"],
            port=config["server"]["port"],
            log_level="info"
        )
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
