# Automatic API Key Rotation System

## Overview

The MindLink Agent now includes an automatic API key rotation system that handles OpenRouter rate limiting (429 errors) by automatically switching between a primary and backup API key. This ensures continuous operation even when rate limits are encountered.

## How It Works

### Key Rotation Logic

1. **Primary Key Usage**: The system starts with the primary API key from `OPENROUTER_API_KEY`
2. **Failure Tracking**: When a 429 rate limit error occurs, the system tracks consecutive failures
3. **Automatic Switch**: After 2 consecutive 429 errors on the current key, it switches to the backup key
4. **Bidirectional Rotation**: If the backup key also encounters 2 consecutive 429 errors, it switches back to the primary key
5. **Success Reset**: On any successful API request, failure counts are reset to zero

### Error Handling Flow

```
Primary Key (Active)
    ↓ 429 Error #1
Primary Key (1 failure)
    ↓ 429 Error #2
Backup Key (Active) ← Switch occurs
    ↓ 429 Error #1
Backup Key (1 failure)
    ↓ 429 Error #2
Primary Key (Active) ← Switch back
```

## Configuration

### Environment Variables

Add both keys to your `.env` file:

```bash
# Primary API key
OPENROUTER_API_KEY=sk-or-v1-your-primary-key-here

# Backup API key for automatic rotation
OPENROUTER_BACKUP_API_KEY=sk-or-v1-your-backup-key-here
```

### Current Configuration

- **Primary Key**: `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`
- **Backup Key**: `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`
- **Failure Threshold**: 2 consecutive 429 errors before switching
- **Retry Logic**: 3 attempts with exponential backoff (1s, 2s, 4s)

## Features

### 🔄 Automatic Failover
- Seamless switching between API keys on rate limit errors
- No manual intervention required
- Maintains service continuity

### 📊 Smart Tracking
- Tracks failures separately for each key
- Resets counters on successful requests
- Prevents unnecessary key switching

### ⚡ Retry Logic
- Exponential backoff on 429 errors
- Maximum 3 retry attempts per request
- Only retries on 429 errors (not other HTTP errors)

### 🔍 Detailed Logging
- Logs key switches with masked key information
- Tracks rate limit encounters
- Provides debugging information

## Implementation Details

### Code Structure

The rotation system is implemented in `mindlink/models/openrouter.py`:

- **APIKeyRotator Class**: Manages key state and rotation logic
- **Global Instance**: Single rotator instance shared across all OpenRouter model instances
- **Integration**: Built into the `generate()` method with retry logic

### Key Methods

```python
# Get current active key
current_key = key_rotator.get_current_key()

# Handle 429 error (triggers rotation if threshold reached)
key_rotator.handle_429_error()

# Reset failure count on successful request
key_rotator.reset_failure_count()
```

## Testing

Run the test script to verify the rotation system:

```bash
python test_key_rotation.py
```

This script simulates 429 errors and verifies:
- Failure counting
- Key switching thresholds
- Bidirectional rotation
- Success reset functionality

## Monitoring

### Log Messages

Watch for these log messages to monitor key rotation:

```
WARNING:mindlink.models.openrouter:[RATE LIMIT] 429 error on attempt 1/3
WARNING:mindlink.models.openrouter:Primary key failed 2 times, switching to backup key
WARNING:mindlink.models.openrouter:[KEY ROTATION] Switched to API key: sk-or-v1...b31d
```

### Key Indicators

- **Rate Limit Encounters**: `[RATE LIMIT]` messages
- **Key Switches**: `[KEY ROTATION]` messages
- **Retry Attempts**: Numbered attempt indicators
- **Exponential Backoff**: Wait time messages

## Benefits

1. **Improved Reliability**: Automatic handling of rate limits
2. **Reduced Downtime**: Seamless failover between keys
3. **Better User Experience**: Transparent error recovery
4. **Cost Optimization**: Efficient use of multiple free tier keys
5. **Operational Simplicity**: No manual key management required

## Troubleshooting

### Common Issues

1. **Both Keys Rate Limited**: If both keys encounter rate limits, the system will continue retrying with exponential backoff
2. **Invalid Backup Key**: Ensure the backup key is valid and has available quota
3. **Missing Environment Variable**: The system falls back to the hardcoded backup key if `OPENROUTER_BACKUP_API_KEY` is not set

### Debug Mode

To see detailed rotation activity, check the logs for:
- Key switch notifications
- Failure count tracking
- Retry attempt information

## Future Enhancements

Potential improvements to consider:
- Support for more than 2 API keys
- Configurable failure thresholds
- Rate limit prediction and proactive switching
- Key usage analytics and reporting
- Integration with external key management systems