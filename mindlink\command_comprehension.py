"""Command Comprehension Module for MindLink Agent.

This module implements intelligent command analysis using Mistral Small 3.1 model
via OpenRouter API to parse user commands and extract intent and entities before
processing by the main LLM.
"""

import json
import logging
import re
import requests
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ConfidenceLevel(Enum):
    """Confidence levels for command analysis."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class CommandAnalysis:
    """Result of command analysis."""
    intent: str
    entities: Dict[str, Any]
    confidence: ConfidenceLevel
    ambiguous_parts: Optional[List[str]] = None
    clarification_needed: bool = False
    raw_command: str = ""


class CommandComprehensionEngine:
    """Engine for intelligent command comprehension using Mistral Small 3.1 model."""
    
    def __init__(self, api_key: str):
        """Initialize the command comprehension engine.
        
        Args:
            api_key: OpenRouter API key for accessing Mistral Small 3.1 model
        """
        self.api_key = api_key
        self.model_name = "mistralai/mistral-small-3.1-24b-instruct:free"
        self.api_url = "https://openrouter.ai/api/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost",
            "X-Title": "MindLink Command Comprehension"
        }
        
        # Pre-defined patterns for quick analysis
        self.intent_patterns = {
            "file_operation": ["create", "write", "read", "delete", "modify", "edit", "file"],
            "code_generation": ["generate", "create code", "write code", "implement", "build"],
            "data_analysis": ["analyze", "process", "calculate", "statistics", "data"],
            "web_search": ["search", "find", "look up", "google", "web"],
            "system_command": ["run", "execute", "command", "shell", "terminal"],
            "question_answer": ["what", "how", "why", "when", "where", "explain", "tell me"]
        }
    
    def analyze_command(self, command: str) -> CommandAnalysis:
        """Analyze a user command to extract intent and entities.
        
        Args:
            command: The user command to analyze
            
        Returns:
            CommandAnalysis object with extracted information
        """
        try:
            # First, try quick pattern matching
            quick_analysis = self._quick_pattern_analysis(command)
            if quick_analysis.confidence == ConfidenceLevel.HIGH:
                return quick_analysis
            
            # If quick analysis is not confident, use Mistral Small 3.1 model
            ai_analysis = self._ai_analysis(command)
            
            # Combine results, preferring AI analysis if available
            if ai_analysis:
                return ai_analysis
            else:
                # Fallback to quick analysis if AI fails
                return quick_analysis
                
        except Exception as e:
            logger.error(f"Error in command analysis: {e}")
            # Return fallback analysis
            return CommandAnalysis(
                intent="unknown",
                entities={},
                confidence=ConfidenceLevel.LOW,
                clarification_needed=True,
                ambiguous_parts=["Unable to analyze command due to error"],
                raw_command=command
            )
    
    def _quick_pattern_analysis(self, command: str) -> CommandAnalysis:
        """Perform quick pattern-based analysis of the command.
        
        Args:
            command: The user command to analyze
            
        Returns:
            CommandAnalysis with pattern-based results
        """
        command_lower = command.lower()
        matched_intents = []
        
        # Check for intent patterns
        for intent, patterns in self.intent_patterns.items():
            if any(pattern in command_lower for pattern in patterns):
                matched_intents.append(intent)
        
        # Extract basic entities
        entities = self._extract_basic_entities(command)
        
        # Determine confidence
        if len(matched_intents) == 1:
            confidence = ConfidenceLevel.HIGH
            intent = matched_intents[0]
            clarification_needed = False
            ambiguous_parts = None
        elif len(matched_intents) > 1:
            confidence = ConfidenceLevel.MEDIUM
            intent = matched_intents[0]  # Take the first match
            clarification_needed = True
            ambiguous_parts = [f"Multiple possible intents: {', '.join(matched_intents)}"]
        else:
            confidence = ConfidenceLevel.LOW
            intent = "general_task"
            clarification_needed = True
            ambiguous_parts = ["No clear intent pattern detected"]
        
        return CommandAnalysis(
            intent=intent,
            entities=entities,
            confidence=confidence,
            clarification_needed=clarification_needed,
            ambiguous_parts=ambiguous_parts,
            raw_command=command
        )
    
    def _extract_basic_entities(self, command: str) -> Dict[str, Any]:
        """Extract basic entities from the command using simple patterns.
        
        Args:
            command: The user command
            
        Returns:
            Dictionary of extracted entities
        """
        entities = {}
        
        # Extract file paths (simple pattern)
        file_patterns = re.findall(r'[\w\-_./\\]+\.[a-zA-Z]{2,4}', command)
        if file_patterns:
            entities['files'] = file_patterns
        
        # Extract numbers
        numbers = re.findall(r'\b\d+\b', command)
        if numbers:
            entities['numbers'] = [int(n) for n in numbers]
        
        # Extract quoted strings (simple approach)
        double_quoted = re.findall(r'"([^"]*)"', command)
        single_quoted = re.findall(r"'([^']*)'", command)
        quoted_strings = double_quoted + single_quoted
        if quoted_strings:
            entities['quoted_text'] = quoted_strings
        
        return entities
    
    def _ai_analysis(self, command: str) -> Optional[CommandAnalysis]:
        """Use Mistral Small 3.1 model for advanced command analysis.
        
        Args:
            command: The user command to analyze
            
        Returns:
            CommandAnalysis from AI model or None if failed
        """
        try:
            # Construct the analysis prompt
            analysis_prompt = self._create_analysis_prompt(command)
            
            # Make API request to Mistral Small 3.1 model
            payload = {
                "model": self.model_name,
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an expert command analysis AI. Analyze user commands to extract intent and entities with high precision."
                    },
                    {
                        "role": "user",
                        "content": analysis_prompt
                    }
                ],
                "temperature": 0.1,
                "max_tokens": 1000
            }
            
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                return self._parse_ai_response(content, command)
            else:
                logger.warning(f"AI analysis failed with status {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error in AI analysis: {e}")
            return None
    
    def _create_analysis_prompt(self, command: str) -> str:
        """Create a structured prompt for the AI model.
        
        Args:
            command: The user command to analyze
            
        Returns:
            Formatted prompt string
        """
        return f"""Analyze the following user command and extract:

1. INTENT: The primary purpose/goal (e.g., file_operation, code_generation, data_analysis, web_search, system_command, question_answer, general_task)
2. ENTITIES: Specific details like files, numbers, names, locations, times, etc.
3. CONFIDENCE: How confident you are in the analysis (high/medium/low)
4. AMBIGUOUS_PARTS: Any unclear or ambiguous parts that need clarification

User Command: "{command}"

Respond in this exact JSON format:
{{
    "intent": "primary_intent_here",
    "entities": {{
        "key1": "value1",
        "key2": ["list_values"]
    }},
    "confidence": "high/medium/low",
    "ambiguous_parts": ["list of unclear parts"],
    "clarification_needed": true/false
}}

Be precise and thorough in your analysis."""
    
    def _parse_ai_response(self, content: str, original_command: str) -> Optional[CommandAnalysis]:
        """Parse the AI model response into CommandAnalysis object.
        
        Args:
            content: Raw response content from AI model
            original_command: The original user command
            
        Returns:
            CommandAnalysis object or None if parsing failed
        """
        try:
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if not json_match:
                logger.warning("No JSON found in AI response")
                return None
            
            json_str = json_match.group(0)
            data = json.loads(json_str)
            
            # Map confidence string to enum
            confidence_map = {
                "high": ConfidenceLevel.HIGH,
                "medium": ConfidenceLevel.MEDIUM,
                "low": ConfidenceLevel.LOW
            }
            
            confidence = confidence_map.get(data.get("confidence", "low"), ConfidenceLevel.LOW)
            
            return CommandAnalysis(
                intent=data.get("intent", "unknown"),
                entities=data.get("entities", {}),
                confidence=confidence,
                ambiguous_parts=data.get("ambiguous_parts"),
                clarification_needed=data.get("clarification_needed", False),
                raw_command=original_command
            )
            
        except Exception as e:
            logger.error(f"Error parsing AI response: {e}")
            return None
    
    def generate_clarification_question(self, analysis: CommandAnalysis) -> str:
        """Generate a clarification question based on analysis results.
        
        Args:
            analysis: The command analysis result
            
        Returns:
            A clarification question string
        """
        if not analysis.clarification_needed or not analysis.ambiguous_parts:
            return ""
        
        base_question = "I need some clarification about your request. "
        
        if len(analysis.ambiguous_parts) == 1:
            return base_question + f"Specifically: {analysis.ambiguous_parts[0]}. Could you please provide more details?"
        else:
            ambiguous_list = "\n".join([f"- {part}" for part in analysis.ambiguous_parts])
            return base_question + f"I found these unclear aspects:\n{ambiguous_list}\n\nCould you please clarify these points?"


def create_command_comprehension_engine(api_key: str) -> CommandComprehensionEngine:
    """Factory function to create a command comprehension engine.
    
    Args:
        api_key: OpenRouter API key
        
    Returns:
        Initialized CommandComprehensionEngine
    """
    return CommandComprehensionEngine(api_key)